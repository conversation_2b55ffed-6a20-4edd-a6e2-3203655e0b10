'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { StripeProvider } from '@/components/checkout/stripe-provider'
import { StripePaymentForm } from '@/components/checkout/stripe-payment-form'
import { createClient } from '@/lib/supabase/client'
import { useAddresses } from '@/hooks/use-addresses'
import { useShippingCountries } from '@/hooks/use-shipping-countries'
import type { User as SupabaseUser } from '@supabase/supabase-js'

import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Gift } from 'lucide-react'
import { formatCurrency, calculateVATByCategory } from '@/lib/utils'
import { useCart } from '@/lib/cart'
import { useToast } from '@/hooks/use-toast'
import { useGiftsReadyForOrder } from '@/hooks/use-gifts-ready-for-order'
import Link from 'next/link'

interface CheckoutFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
  shippingAddress: {
    firstName: string
    lastName: string
    company: string
    street: string
    city: string
    postalCode: string
    country: string
  }
  billingAddress: {
    firstName: string
    lastName: string
    company: string
    street: string
    city: string
    postalCode: string
    country: string
  }
  billingSameAsShipping: boolean
  createAccount: boolean
  marketingConsent: boolean
}

export default function CheckoutPage() {
  console.log('🛒 CheckoutPage: Component rendering')
  const router = useRouter()
  const { cart, clearCart, loading: cartLoading } = useCart()
  const { toast } = useToast()
  const t = useTranslations('checkout')
  const locale = useLocale()
  const { countries: shippingCountries, loading: shippingCountriesLoading, getShippingCost } = useShippingCountries()
  const [loading, setLoading] = useState(false)
  const [skipCartRedirect, setSkipCartRedirect] = useState(false)
  const [paymentIntent, setPaymentIntent] = useState<{
    id: string
    client_secret: string
    status: string
  } | null>(null)
  const [showPaymentForm, setShowPaymentForm] = useState(false)

  // Coupon states
  const [couponCode, setCouponCode] = useState('')
  const [appliedCoupon, setAppliedCoupon] = useState<{
    id: string
    code: string
    type: string
    value: number
    discountAmount: number
  } | null>(null)
  const [leagueDiscount, setLeagueDiscount] = useState<{
    percentage: number
    amount: number
    league: number
    levelName: string
  } | null>(null)
  const [couponLoading, setCouponLoading] = useState(false)

  // VAT settings state
  const [vatSettings, setVatSettings] = useState({
    use_category_vat: false,
    vat_rate: 0.077,
    vat_rate_coffee: 0.077,
    vat_rate_accessories: 0.077
  })

  // User authentication states
  const [user, setUser] = useState<SupabaseUser | null>(null)
  const [userProfile, setUserProfile] = useState<{
    id: string;
    email: string;
    first_name: string;
    last_name: string;
    phone?: string;
    created_at: string;
    updated_at: string;
    lifetime_spend: number;
    current_level: number;
    total_points: number;
    is_admin: boolean;
  } | null>(null)
  const [userLoading, setUserLoading] = useState(true)
  const supabase = createClient()

  // Only log detailed state in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🛒 CheckoutPage: State', {
      cartLoading,
      cartItemsCount: cart.items.length,
      cartId: cart.id,
      locale,
      appliedCoupon: appliedCoupon ? { code: appliedCoupon.code, discountAmount: appliedCoupon.discountAmount } : null,
      userLoggedIn: !!user
    })
  }
  const [formData, setFormData] = useState<CheckoutFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    shippingAddress: {
      firstName: '',
      lastName: '',
      company: '',
      street: '',
      city: '',
      postalCode: '',
      country: 'CH'
    },
    billingAddress: {
      firstName: '',
      lastName: '',
      company: '',
      street: '',
      city: '',
      postalCode: '',
      country: 'CH'
    },
    billingSameAsShipping: true,
    createAccount: false,
    marketingConsent: false
  })

  // Use addresses hook for logged-in users
  const { addresses } = useAddresses(user)

  // Update default country when shipping countries are loaded
  useEffect(() => {
    if (!shippingCountriesLoading && shippingCountries.length > 0) {
      // Check if current country is still valid, otherwise set to first available
      const currentCountryValid = shippingCountries.some(c => c.code === formData.shippingAddress.country)
      if (!currentCountryValid) {
        const defaultCountry = shippingCountries.find(c => c.code === 'CH') || shippingCountries[0]
        setFormData(prev => ({
          ...prev,
          shippingAddress: { ...prev.shippingAddress, country: defaultCountry.code },
          billingAddress: { ...prev.billingAddress, country: defaultCountry.code }
        }))
      }
    }
  }, [shippingCountries, shippingCountriesLoading, formData.shippingAddress.country])

  // Get gifts ready for order
  const { gifts: giftsReadyForOrder } = useGiftsReadyForOrder(user?.id)

  // Load user data and populate form
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setUserLoading(true)

        // Handle AuthSessionMissingError gracefully for guest checkout
        let currentUser = null
        let authError = null

        try {
          const { data: { user }, error } = await supabase.auth.getUser()
          currentUser = user
          authError = error
        } catch (error) {
          // Handle AuthSessionMissingError and other auth errors gracefully
          if (error instanceof Error && error.message.includes('Auth session missing')) {
            console.log('🛒 CheckoutPage: No auth session found - processing as guest checkout')
            currentUser = null
            authError = null
          } else {
            console.error('🛒 CheckoutPage: Unexpected error getting user:', error)
            authError = error
          }
        }

        if (authError) {
          console.error('Error getting user:', authError)
          setUser(null)
          return
        }

        setUser(currentUser)

        // If user is logged in, populate form with user data
        if (currentUser) {
          console.log('🛒 CheckoutPage: User logged in, populating form data')

          // Get user profile data from database
          const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', currentUser.id)
            .single()

          if (profileError) {
            console.error('Error getting user profile:', profileError)
          } else if (profile) {
            // Set the complete user profile for addresses hook
            setUserProfile(profile)
          }

          // Populate basic contact info
          setFormData(prev => ({
            ...prev,
            firstName: profile?.first_name || currentUser.user_metadata?.first_name || '',
            lastName: profile?.last_name || currentUser.user_metadata?.last_name || '',
            email: currentUser.email || '',
            phone: profile?.phone || currentUser.user_metadata?.phone || '',
            createAccount: false // User is already logged in
          }))
        } else {
          console.log('🛒 CheckoutPage: No authenticated user - guest checkout mode')
        }
      } catch (error) {
        console.error('Error loading user data:', error)
        setUser(null)
      } finally {
        setUserLoading(false)
      }
    }

    loadUserData()
  }, [supabase])

  // Populate addresses when addresses are loaded
  useEffect(() => {
    if (userProfile && addresses.length > 0) {
      console.log('🛒 CheckoutPage: Populating addresses from user data')

      // Find default shipping address
      const defaultShipping = addresses.find(addr => addr.type === 'shipping' && addr.is_default)
      const defaultBilling = addresses.find(addr => addr.type === 'billing' && addr.is_default)

      // Use first shipping address if no default
      const shippingAddress = defaultShipping || addresses.find(addr => addr.type === 'shipping')
      const billingAddress = defaultBilling || addresses.find(addr => addr.type === 'billing')

      if (shippingAddress) {
        setFormData(prev => ({
          ...prev,
          shippingAddress: {
            firstName: shippingAddress.first_name,
            lastName: shippingAddress.last_name,
            company: shippingAddress.company || '',
            street: shippingAddress.street_address,
            city: shippingAddress.city,
            postalCode: shippingAddress.postal_code,
            country: shippingAddress.country
          }
        }))
      }

      if (billingAddress) {
        setFormData(prev => ({
          ...prev,
          billingAddress: {
            firstName: billingAddress.first_name,
            lastName: billingAddress.last_name,
            company: billingAddress.company || '',
            street: billingAddress.street_address,
            city: billingAddress.city,
            postalCode: billingAddress.postal_code,
            country: billingAddress.country
          },
          billingSameAsShipping: false
        }))
      }
    }
  }, [userProfile, addresses])

  // Load user's league discount
  useEffect(() => {
    const loadUserLeagueDiscount = async () => {
      if (!user) {
        setLeagueDiscount(null)
        return
      }

      try {
        const response = await fetch('/api/user/league-discount')
        if (response.ok) {
          const data = await response.json()
          if (data.discount) {
            setLeagueDiscount(data.discount)
          }
        }
      } catch (error) {
        console.error('Error loading league discount:', error)
      }
    }

    loadUserLeagueDiscount()
  }, [user])

  // Calculate totals - prices are VAT-inclusive
  const subtotal = cart.items.reduce((sum, item) => {
    const price = item.product?.discount_price || item.product?.price || 0
    return sum + (price * item.quantity)
  }, 0)

  const shippingCost = shippingCountriesLoading ? 0 : getShippingCost(formData.shippingAddress.country, subtotal)
  const couponDiscountAmount = appliedCoupon?.discountAmount || 0
  const leagueDiscountAmount = leagueDiscount ? (subtotal * leagueDiscount.percentage / 100) : 0
  const totalDiscountAmount = couponDiscountAmount + leagueDiscountAmount
  const total = subtotal + shippingCost - totalDiscountAmount // Total is VAT-inclusive

  // Calculate VAT breakdown by category for display (VAT is included in prices)
  const vatBreakdown = calculateVATByCategory(
    cart.items.map(item => ({
      quantity: item.quantity,
      products: {
        price: item.product?.price || 0,
        discount_price: item.product?.discount_price,
        category: (item.product?.category as 'coffee' | 'accessories') || 'coffee'
      }
    })),
    vatSettings
  )

  // Calculate VAT on shipping and discount (using general VAT rate)
  const shippingVAT = shippingCost > 0 ? shippingCost - (shippingCost / (1 + (vatSettings.vat_rate || 0.077))) : 0
  const discountVAT = totalDiscountAmount > 0 ? totalDiscountAmount - (totalDiscountAmount / (1 + (vatSettings.vat_rate || 0.077))) : 0

  const taxAmount = vatBreakdown.totalVAT + shippingVAT - discountVAT

  // Load VAT settings
  useEffect(() => {
    const loadVATSettings = async () => {
      try {
        const supabase = createClient()
        const { data } = await supabase
          .from('site_settings')
          .select('use_category_vat, vat_rate, vat_rate_coffee, vat_rate_accessories')
          .maybeSingle()

        if (data) {
          setVatSettings({
            use_category_vat: data.use_category_vat || false,
            vat_rate: data.vat_rate || 0.077,
            vat_rate_coffee: data.vat_rate_coffee || 0.077,
            vat_rate_accessories: data.vat_rate_accessories || 0.077
          })
        }
      } catch (error) {
        console.error('Error loading VAT settings:', error)
      }
    }

    loadVATSettings()
  }, [])

  useEffect(() => {
    console.log('🛒 CheckoutPage: useEffect triggered', {
      cartLoading,
      cartItemsLength: cart.items.length,
      skipCartRedirect
    })

    if (!skipCartRedirect && !cartLoading && cart.items.length === 0) {
      console.log('🛒 CheckoutPage: Redirecting to cart - no items')
      router.push(`/${locale}/cart`)
    }
  }, [cart.items.length, cartLoading, router, locale, skipCartRedirect])

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof CheckoutFormData] as Record<string, unknown>),
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
  }

  const handleCheckboxChange = (field: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked
    }))

    // Copy shipping to billing if checkbox is checked
    if (field === 'billingSameAsShipping' && checked) {
      setFormData(prev => ({
        ...prev,
        billingAddress: { ...prev.shippingAddress }
      }))
    }

    // If unchecking billingSameAsShipping, try to preload saved billing address
    if (field === 'billingSameAsShipping' && !checked && addresses.length > 0) {
      const defaultBilling = addresses.find(addr => addr.type === 'billing' && addr.is_default)
      const billingAddress = defaultBilling || addresses.find(addr => addr.type === 'billing')

      if (billingAddress) {
        setFormData(prev => ({
          ...prev,
          billingAddress: {
            firstName: billingAddress.first_name,
            lastName: billingAddress.last_name,
            company: billingAddress.company || '',
            street: billingAddress.street_address,
            city: billingAddress.city,
            postalCode: billingAddress.postal_code,
            country: billingAddress.country
          }
        }))
      }
    }
  }

  const validateForm = () => {
    const required = [
      'firstName', 'lastName', 'email', 'phone',
      'shippingAddress.firstName', 'shippingAddress.lastName',
      'shippingAddress.street', 'shippingAddress.city', 'shippingAddress.postalCode'
    ]

    // Add billing address validation if not same as shipping
    if (!formData.billingSameAsShipping) {
      required.push(
        'billingAddress.firstName', 'billingAddress.lastName',
        'billingAddress.street', 'billingAddress.city', 'billingAddress.postalCode'
      )
    }

    for (const field of required) {
      if (field.includes('.')) {
        const [parent, child] = field.split('.')
        const parentValue = formData[parent as keyof CheckoutFormData] as Record<string, string>
        if (!parentValue[child]) {
          return false
        }
      } else {
        if (!formData[field as keyof CheckoutFormData]) {
          return false
        }
      }
    }

    if (!formData.billingSameAsShipping) {
      const billingRequired = [
        'billingAddress.street', 'billingAddress.city', 'billingAddress.postalCode'
      ]
      for (const field of billingRequired) {
        const [parent, child] = field.split('.')
        const parentValue = formData[parent as keyof CheckoutFormData] as Record<string, string>
        if (!parentValue[child]) {
          return false
        }
      }
    }

    // Validate shipping country is available
    if (!shippingCountries.some(c => c.code === formData.shippingAddress.country)) {
      return false
    }

    // Validate billing country if different from shipping
    if (!formData.billingSameAsShipping && !shippingCountries.some(c => c.code === formData.billingAddress.country)) {
      return false
    }

    return true
  }

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast({
        title: t('error'),
        description: t('enterCouponCode'),
        variant: 'destructive'
      })
      return
    }

    setCouponLoading(true)

    try {
      const response = await fetch('/api/checkout/validate-coupon', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: couponCode.trim(),
          orderAmount: subtotal + shippingCost
        }),
      })

      const result = await response.json()

      if (result.success) {
        setAppliedCoupon(result.coupon)
        toast({
          title: t('couponApplied'),
          description: t('couponSaved', { amount: formatCurrency(result.discountAmount) }),
          variant: 'default'
        })
      } else {
        toast({
          title: t('error'),
          description: result.error || t('invalidCoupon'),
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error applying coupon:', error)
      toast({
        title: t('error'),
        description: t('invalidCoupon'),
        variant: 'destructive'
      })
    } finally {
      setCouponLoading(false)
    }
  }

  const handleRemoveCoupon = () => {
    setAppliedCoupon(null)
    setCouponCode('')
    toast({
      title: t('couponRemoved'),
      description: t('couponRemovedDesc'),
      variant: 'default'
    })
  }

  const handleCreateOrder = async () => {
    if (!validateForm()) {
      toast({
        title: t('error'),
        description: t('fillRequiredFields'),
        variant: 'destructive'
      })
      return
    }

    setLoading(true)

    try {
      const orderData = {
        cartId: cart.id,
        customerInfo: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone
        },
        shippingAddress: formData.shippingAddress,
        billingAddress: formData.billingSameAsShipping
          ? formData.shippingAddress
          : formData.billingAddress,
        total: total,
        coupon: appliedCoupon ? {
          id: appliedCoupon.id,
          code: appliedCoupon.code,
          discountAmount: appliedCoupon.discountAmount
        } : null
      }

      const response = await fetch('/api/checkout/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(orderData),
      })

      const result = await response.json()

      if (result.success && result.paymentIntent) {
        setPaymentIntent(result.paymentIntent)
        setShowPaymentForm(true)
      } else {
        toast({
          title: t('error'),
          description: result.error || t('orderCreationFailed'),
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Order creation error:', error)
      toast({
        title: t('error'),
        description: t('unexpectedError'),
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentSuccess = (paymentIntentId: string) => {
    setSkipCartRedirect(true)
    clearCart()
    router.push(`/${locale}/checkout/success?payment_intent=${paymentIntentId}`)
  }

  const handlePaymentError = (error: string) => {
    toast({
      title: t('paymentFailed'),
      description: error,
      variant: 'destructive'
    })
  }

  if (cartLoading) {
    console.log('🛒 CheckoutPage: Showing loading state')
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p>Caricamento checkout...</p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (cart.items.length === 0) {
    console.log('🛒 CheckoutPage: Showing empty cart state')
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="p-8 text-center">
              <p className="mb-4">Il carrello è vuoto</p>
              <Button onClick={() => router.push(`/${locale}/shop`)}>
                Continua lo shopping
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link href={`/${locale}/cart`} prefetch={false}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t('backToCart')}
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">{t('title')}</h1>
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Checkout Form */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>{t('contactInfo')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {userLoading && (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  <span className="text-sm text-muted-foreground">{t('loadingUserData')}</span>
                </div>
              )}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="firstName">{t('firstName')} {t('required')}</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    disabled={userLoading}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">{t('lastName')} {t('required')}</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    disabled={userLoading}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="email">{t('email')} {t('required')}</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={userLoading}
                  required
                />
              </div>
              <div>
                <Label htmlFor="phone">{t('phone')} {t('required')}</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={userLoading}
                  required
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>{t('shippingAddress')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="shippingFirstName">{t('firstName')} <span className="text-red-500">{t('required')}</span></Label>
                  <Input
                    id="shippingFirstName"
                    placeholder={t('firstName')}
                    value={formData.shippingAddress.firstName}
                    onChange={(e) => handleInputChange('shippingAddress.firstName', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="shippingLastName">{t('lastName')} <span className="text-red-500">{t('required')}</span></Label>
                  <Input
                    id="shippingLastName"
                    placeholder={t('lastName')}
                    value={formData.shippingAddress.lastName}
                    onChange={(e) => handleInputChange('shippingAddress.lastName', e.target.value)}
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="shippingCompany">{t('company')}</Label>
                <Input
                  id="shippingCompany"
                  placeholder={t('company')}
                  value={formData.shippingAddress.company}
                  onChange={(e) => handleInputChange('shippingAddress.company', e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="shippingStreet">{t('street')} <span className="text-red-500">{t('required')}</span></Label>
                <Input
                  id="shippingStreet"
                  placeholder={t('street')}
                  value={formData.shippingAddress.street}
                  onChange={(e) => handleInputChange('shippingAddress.street', e.target.value)}
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="shippingPostalCode">{t('postalCode')} <span className="text-red-500">{t('required')}</span></Label>
                  <Input
                    id="shippingPostalCode"
                    placeholder={t('postalCode')}
                    value={formData.shippingAddress.postalCode}
                    onChange={(e) => handleInputChange('shippingAddress.postalCode', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="shippingCity">{t('city')} <span className="text-red-500">{t('required')}</span></Label>
                  <Input
                    id="shippingCity"
                    placeholder={t('city')}
                    value={formData.shippingAddress.city}
                    onChange={(e) => handleInputChange('shippingAddress.city', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="shippingCountry">{t('country')} <span className="text-red-500">{t('required')}</span></Label>
                  <Select value={formData.shippingAddress.country} onValueChange={(value) => handleInputChange('shippingAddress.country', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder={t('country')} />
                    </SelectTrigger>
                    <SelectContent>
                      {shippingCountriesLoading ? (
                        <SelectItem value="loading" disabled>
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          Caricamento...
                        </SelectItem>
                      ) : shippingCountries.length > 0 ? (
                        shippingCountries.map((country) => (
                          <SelectItem key={country.code} value={country.code}>
                            {country.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-countries" disabled>
                          Nessun paese disponibile per la spedizione
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="billingSameAsShipping"
              checked={formData.billingSameAsShipping}
              onCheckedChange={(checked) => handleCheckboxChange('billingSameAsShipping', checked as boolean)}
            />
            <Label htmlFor="billingSameAsShipping">
              {t('billingSameAsShipping')}
            </Label>
          </div>

          {/* Billing Address - Only show when different from shipping */}
          {!formData.billingSameAsShipping && (
            <Card>
              <CardHeader>
                <CardTitle>{t('billingAddress')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="billingFirstName">{t('firstName')} <span className="text-red-500">{t('required')}</span></Label>
                    <Input
                      id="billingFirstName"
                      placeholder={t('firstName')}
                      value={formData.billingAddress.firstName}
                      onChange={(e) => handleInputChange('billingAddress.firstName', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="billingLastName">{t('lastName')} <span className="text-red-500">{t('required')}</span></Label>
                    <Input
                      id="billingLastName"
                      placeholder={t('lastName')}
                      value={formData.billingAddress.lastName}
                      onChange={(e) => handleInputChange('billingAddress.lastName', e.target.value)}
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="billingCompany">{t('company')}</Label>
                  <Input
                    id="billingCompany"
                    placeholder={t('company')}
                    value={formData.billingAddress.company}
                    onChange={(e) => handleInputChange('billingAddress.company', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="billingStreet">{t('street')} <span className="text-red-500">{t('required')}</span></Label>
                  <Input
                    id="billingStreet"
                    placeholder={t('street')}
                    value={formData.billingAddress.street}
                    onChange={(e) => handleInputChange('billingAddress.street', e.target.value)}
                    required
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="billingPostalCode">{t('postalCode')} <span className="text-red-500">{t('required')}</span></Label>
                    <Input
                      id="billingPostalCode"
                      placeholder={t('postalCode')}
                      value={formData.billingAddress.postalCode}
                      onChange={(e) => handleInputChange('billingAddress.postalCode', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="billingCity">{t('city')} <span className="text-red-500">{t('required')}</span></Label>
                    <Input
                      id="billingCity"
                      placeholder={t('city')}
                      value={formData.billingAddress.city}
                      onChange={(e) => handleInputChange('billingAddress.city', e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="billingCountry">{t('country')} <span className="text-red-500">{t('required')}</span></Label>
                    <Select value={formData.billingAddress.country} onValueChange={(value) => handleInputChange('billingAddress.country', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('country')} />
                      </SelectTrigger>
                      <SelectContent>
                        {shippingCountriesLoading ? (
                          <SelectItem value="loading" disabled>
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Caricamento...
                          </SelectItem>
                        ) : shippingCountries.length > 0 ? (
                          shippingCountries.map((country) => (
                            <SelectItem key={country.code} value={country.code}>
                              {country.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-countries" disabled>
                            Nessun paese disponibile per la spedizione
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Order Summary */}
        <div>
          <Card className="sticky top-4">
            <CardHeader>
              <CardTitle>{t('orderSummary')}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                {cart.items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <div className="flex-1">
                      <p className="font-medium">{item.product?.title}</p>
                      <p className="text-sm text-gray-600">{t('quantity')}: {item.quantity}</p>
                    </div>
                    <p className="font-medium">
                      {formatCurrency((item.product?.discount_price || item.product?.price || 0) * item.quantity)}
                    </p>
                  </div>
                ))}
              </div>

              {/* Gifts Section */}
              {giftsReadyForOrder.length > 0 && (
                <div className="border-t pt-4">
                  <h4 className="font-medium text-green-700 mb-3 flex items-center gap-2">
                    <Gift className="h-4 w-4" />
                    Regali inclusi
                  </h4>
                  <div className="space-y-3">
                    {giftsReadyForOrder.map((gift) => (
                      <div key={gift.id} className="flex justify-between items-center bg-green-50 p-3 rounded-lg">
                        <div className="flex-1">
                          <p className="font-medium text-green-800">{gift.products[0]?.title}</p>
                          <p className="text-sm text-green-600">
                            Regalo {gift.gift_rewards[0]?.type === 'league' ? 'Lega' : 'Livello'} {gift.gift_rewards[0]?.trigger_value}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-700">GRATIS</p>
                          <p className="text-xs text-green-600">
                            Valore: {formatCurrency(gift.products[0]?.price || 0)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="border-t pt-4 space-y-2">
                <div className="flex justify-between">
                  <span>{t('subtotal')}:</span>
                  <span>{formatCurrency(subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span>{t('shipping')}:</span>
                  <span>{shippingCost === 0 ? t('free') : formatCurrency(shippingCost)}</span>
                </div>
                {leagueDiscount && (
                  <div className="flex justify-between text-blue-600">
                    <span>{t('leagueDiscount')} ({leagueDiscount.levelName}):</span>
                    <span>-{formatCurrency(leagueDiscountAmount)}</span>
                  </div>
                )}
                {appliedCoupon && (
                  <div className="flex justify-between text-green-600">
                    <span>{t('discount')} ({appliedCoupon.code}):</span>
                    <span>-{formatCurrency(appliedCoupon.discountAmount)}</span>
                  </div>
                )}
                <div className="flex justify-between font-bold text-lg border-t pt-2">
                  <span>{t('total')}:</span>
                  <span>{formatCurrency(total)}</span>
                </div>
                <div className="flex justify-between text-sm text-muted-foreground">
                  <span>
                    {vatSettings.use_category_vat ? (
                      <>
                        {t('vatBreakdown')}:
                        {vatBreakdown.coffeeVAT > 0 && (
                          <span className="block text-xs">
                            {t('coffee')}: {formatCurrency(vatBreakdown.coffeeVAT)} ({(vatSettings.vat_rate_coffee * 100).toFixed(1)}%)
                          </span>
                        )}
                        {vatBreakdown.accessoriesVAT > 0 && (
                          <span className="block text-xs">
                            {t('accessories')}: {formatCurrency(vatBreakdown.accessoriesVAT)} ({(vatSettings.vat_rate_accessories * 100).toFixed(1)}%)
                          </span>
                        )}
                      </>
                    ) : (
                      t('vatIncluded', { rate: (vatSettings.vat_rate * 100).toFixed(1) })
                    )}
                  </span>
                  <span>{formatCurrency(taxAmount)}</span>
                </div>
              </div>

              {/* Coupon Section */}
              <div className="border-t pt-4">
                <div className="space-y-3">
                  <Label htmlFor="couponCode">{t('couponCode')}</Label>
                  {!appliedCoupon ? (
                    <div className="flex gap-2">
                      <Input
                        id="couponCode"
                        placeholder={t('enterCouponCode')}
                        value={couponCode}
                        onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                        disabled={couponLoading}
                      />
                      <Button
                        type="button"
                        variant="outline"
                        onClick={handleApplyCoupon}
                        disabled={couponLoading || !couponCode.trim()}
                      >
                        {couponLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          t('applyCoupon')
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center gap-2">
                        <span className="text-green-700 font-medium">
                          {appliedCoupon.code}
                        </span>
                        <span className="text-green-600 text-sm">
                          -{formatCurrency(appliedCoupon.discountAmount)}
                        </span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={handleRemoveCoupon}
                        className="text-green-700 hover:text-green-800"
                      >
                        {t('removeCoupon')}
                      </Button>
                    </div>
                  )}
                </div>
              </div>

              {!showPaymentForm ? (
                <Button
                  onClick={handleCreateOrder}
                  className="w-full"
                  size="lg"
                  disabled={loading || shippingCountriesLoading}
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('processing')}
                    </>
                  ) : (
                    t('continueToPayment')
                  )}
                </Button>
              ) : null}
            </CardContent>
          </Card>

          {/* Payment Form */}
          {showPaymentForm && paymentIntent && (
            <div className="mt-6 relative">
              {/* Enhanced backdrop for better readability */}
              <div className="sticky top-4 z-10">
                <div className="relative">
                  {/* Backdrop blur effect */}
                  <div className="absolute inset-0 bg-background/80 backdrop-blur-sm rounded-lg -m-2 shadow-lg border border-border/50"></div>

                  {/* Payment form content */}
                  <div className="relative z-10">
                    <StripeProvider clientSecret={paymentIntent.client_secret}>
                      <StripePaymentForm
                        clientSecret={paymentIntent.client_secret}
                        onSuccess={handlePaymentSuccess}
                        onError={handlePaymentError}
                        amount={total}
                        loading={loading}
                      />
                    </StripeProvider>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
